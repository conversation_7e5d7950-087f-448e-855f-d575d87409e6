// Code.gs

const GROQ_API_KEY = '********************************************************';
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';
const GROQ_MODEL   = 'llama3-70b-8192';

function onOpen() {
  try {
    SlidesApp.getUi()
      .createMenu('AI Generation')
      .addItem('Launch AI Slide Creator', 'openSidebar')
      .addToUi();
  } catch (e) {}
}

function openSidebar() {
  try {
    const html = HtmlService.createHtmlOutputFromFile('Sidebar')
      .setTitle('AI Slide Creator')
      .setWidth(400);
    SlidesApp.getUi().showSidebar(html);
  } catch (e) {}
}

/**
 * Create Slides mode
 * config = {
 *   topic: string,
 *   author: string,
 *   theme: string,
 *   slideCount: number,
 *   slides: [ ... slideDescriptions ... ]
 * }
 */
function generateSlides(config) {
  if (!config.slides) {
    throw new Error("Missing `slides` array.");
  }
  const topic      = config.topic;
  const author     = config.author;
  const slideCount = parseInt(config.slideCount, 10);
  const slidesDesc = config.slides; // array of strings
  const theme      = config.theme;

  if (slidesDesc.length !== slideCount) {
    throw new Error("Slide count mismatch.");
  }

  const pres = SlidesApp.create(`${topic} Presentation`);
  const firstSlide = pres.getSlides()[0];
  firstSlide.getPageElements().forEach(e => e.remove());

  const pageWidth  = pres.getPageWidth();
  const pageHeight = pres.getPageHeight();

  let bgColor, rectColor, textColor;
  switch (theme) {
    case 'Dark':
      bgColor   = '#333333'; rectColor = '#555555'; textColor = '#FFFFFF'; break;
    case 'Blue':
      bgColor   = '#e3f2fd'; rectColor = '#bbdefb'; textColor = '#000000'; break;
    case 'Green':
      bgColor   = '#e8f5e9'; rectColor = '#c8e6c9'; textColor = '#000000'; break;
    default:
      bgColor   = '#f2f2f2'; rectColor = '#FFFFFF'; textColor = '#000000'; break;
  }

  slidesDesc.forEach((descRaw, idx) => {
    const desc = descRaw.trim();
    let prompt = '';
    let expects = [];

    // Decide prompt based on slide index and whether description is nonempty
    const escapedDesc = desc.replace(/\\/g, '\\\\').replace(/"/g, '\\"');

    if (idx === 0) {
      // Slide 1: always Title + Content + Decorate
      if (desc === '') {
        prompt = [
          `You are a professional presentation assistant.`,
          `Topic: ${topic.replace(/\\/g,'\\\\').replace(/"/g,'\\"')}`,
          `Slide Type: Slide 1 (Title, Why, Whom)`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Title": a concise slide title,`,
          `  "Content": one to two paragraphs explaining why this topic matters and whom it concerns,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      } else {
        prompt = [
          `You are a professional presentation assistant.`,
          `Topic: ${topic.replace(/\\/g,'\\\\').replace(/"/g,'\\"')}`,
          `Slide Description: ${escapedDesc}`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Title": a concise slide title,`,
          `  "Content": one to two paragraphs of clear, engaging text based on the above description,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      }
      expects = ["Title","Content","Decorate"];
    }
    else if (idx === 1) {
      // Slide 2: Content + Decorate
      if (desc === '') {
        prompt = [
          `You are a professional presentation assistant.`,
          `Topic: ${topic.replace(/\\/g,'\\\\').replace(/"/g,'\\"')}`,
          `Slide Type: Slide 2 (Introduction)`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Content": one short introductory paragraph,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      } else {
        prompt = [
          `You are a professional presentation assistant.`,
          `Slide Description: ${escapedDesc}`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Content": one to two paragraphs of clear, engaging text,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      }
      expects = ["Content","Decorate"];
    }
    else {
      // Slide 3+:
      if (desc === '') {
        prompt = [
          `You are a professional presentation assistant.`,
          `Topic: ${topic.replace(/\\/g,'\\\\').replace(/"/g,'\\"')}`,
          `Slide Type: Slide ${idx+1} (You Decide)`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Content": one to two paragraphs about the next logical point,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      } else {
        prompt = [
          `You are a professional presentation assistant.`,
          `Slide Description: ${escapedDesc}`,
          `Instruction: Provide EXACT JSON with these keys:`,
          `  "Content": one to two paragraphs of clear, engaging text,`,
          `  "Decorate": true or false.`,
          `Escape any double quotes with backslashes.`,
          `Return only the JSON object.`
        ].join('\n');
      }
      expects = ["Content","Decorate"];
    }

    const responseRaw = callGroqWithErrors(prompt);
    const jsonText = extractJson(responseRaw);
    let slideObj;
    try {
      slideObj = JSON.parse(jsonText);
    } catch (e) {
      throw new Error(`Failed to parse JSON for slide ${idx+1}: ${jsonText}`);
    }
    expects.forEach(key => {
      if (!(key in slideObj)) {
        throw new Error(`Missing '${key}' in slide ${idx+1} data.`);
      }
    });

    const slide = (idx === 0) ? firstSlide : pres.appendSlide(SlidesApp.PredefinedLayout.BLANK);
    slide.getBackground().setSolidFill(bgColor);

    if (slideObj.Decorate === true) {
      const fullRect = slide.insertShape(
        SlidesApp.ShapeType.RECTANGLE,
        0, 0, pageWidth, pageHeight
      );
      fullRect.getFill().setSolidFill(rectColor);
      fullRect.getBorder().setTransparent();
      fullRect.sendToBack();
    }

    if (idx === 0) {
      const titleBox = slide.insertTextBox(
        slideObj.Title,
        pageWidth * 0.05, pageHeight * 0.03,
        pageWidth * 0.90, pageHeight * 0.15
      );
      titleBox.getText().getTextStyle()
        .setFontFamily('Roboto').setFontSize(32).setBold(true).setForegroundColor(textColor);

      const contentBox = slide.insertTextBox(
        slideObj.Content,
        pageWidth * 0.05, pageHeight * 0.20,
        pageWidth * 0.90, pageHeight * 0.60
      );
      const contentRange = contentBox.getText();
      contentRange.getTextStyle()
        .setFontFamily('Roboto').setFontSize(18).setForegroundColor(textColor);
      _applyTagAndRemoveTags(contentRange, "<b>", "</b>", tr => tr.setBold(true).setForegroundColor(textColor));
      _applyTagAndRemoveTags(contentRange, "<u>", "</u>", tr => tr.setUnderline(true).setForegroundColor(textColor));
      _applyFontTagAndRemove(contentRange, textColor);
      _stripAnyRemainingAngleTags(contentRange);

      const byBox = slide.insertTextBox(
        `By: ${author}`,
        pageWidth * 0.05, pageHeight * 0.85,
        pageWidth * 0.90, pageHeight * 0.10
      );
      byBox.getText().getTextStyle()
        .setFontFamily('Roboto').setFontSize(16).setItalic(true).setForegroundColor(textColor);
    } else {
      const contentBox = slide.insertTextBox(
        slideObj.Content,
        pageWidth * 0.05, pageHeight * 0.05,
        pageWidth * 0.90, pageHeight * 0.90
      );
      const contentRange = contentBox.getText();
      contentRange.getTextStyle()
        .setFontFamily('Roboto').setFontSize(18).setForegroundColor(textColor);
      _applyTagAndRemoveTags(contentRange, "<b>", "</b>", tr => tr.setBold(true).setForegroundColor(textColor));
      _applyTagAndRemoveTags(contentRange, "<u>", "</u>", tr => tr.setUnderline(true).setForegroundColor(textColor));
      _applyFontTagAndRemove(contentRange, textColor);
      _stripAnyRemainingAngleTags(contentRange);
    }
  });

  return pres.getUrl();
}


/**
 * Organize Slides mode
 * config = { topic: string, author: string, theme: string, content: string }
 */
function organizeSlides(config) {
  const topic   = config.topic;
  const author  = config.author;
  const theme   = config.theme;
  const content = config.content.replace(/\\/g,'\\\\').replace(/"/g,'\\"');

  const prompt = [
    `You are a professional presentation assistant.`,
    `Topic: ${topic.replace(/\\/g,'\\\\').replace(/"/g,'\\"')}`,
    `Content to organize: ${content}`,
    `Instruction: Split the above content into logical slides. Provide EXACT JSON in this form:`,
    `  {`,
    `    "Slides": [`,
    `      { "Title": "<slide1 title>", "Content": "<slide1 content>", "Decorate": true/false },`,
    `      { "Title": "<slide2 title>", "Content": "<slide2 content>", "Decorate": true/false },`,
    `      ...`,
    `    ]`,
    `  }`,
    `Escape any double quotes with backslashes.`,
    `Return only the JSON object.`
  ].join('\n');

  const responseRaw = callGroqWithErrors(prompt);
  const jsonText = extractJson(responseRaw);
  let obj;
  try {
    obj = JSON.parse(jsonText);
  } catch (e) {
    throw new Error(`Failed to parse JSON from AI: ${jsonText}`);
  }
  if (!obj.Slides || !Array.isArray(obj.Slides) || obj.Slides.length === 0) {
    throw new Error("AI did not return a valid Slides array.");
  }

  const pres = SlidesApp.create(`${topic} Presentation`);
  const firstSlide = pres.getSlides()[0];
  firstSlide.getPageElements().forEach(e => e.remove());

  const pageWidth  = pres.getPageWidth();
  const pageHeight = pres.getPageHeight();

  let bgColor, rectColor, textColor;
  switch (theme) {
    case 'Dark':
      bgColor   = '#333333'; rectColor = '#555555'; textColor = '#FFFFFF'; break;
    case 'Blue':
      bgColor   = '#e3f2fd'; rectColor = '#bbdefb'; textColor = '#000000'; break;
    case 'Green':
      bgColor   = '#e8f5e9'; rectColor = '#c8e6c9'; textColor = '#000000'; break;
    default:
      bgColor   = '#f2f2f2'; rectColor = '#FFFFFF'; textColor = '#000000'; break;
  }

  obj.Slides.forEach((slideObj, idx) => {
    const slide = (idx === 0) ? firstSlide : pres.appendSlide(SlidesApp.PredefinedLayout.BLANK);
    slide.getBackground().setSolidFill(bgColor);

    if (slideObj.Decorate === true) {
      const fullRect = slide.insertShape(
        SlidesApp.ShapeType.RECTANGLE,
        0, 0, pageWidth, pageHeight
      );
      fullRect.getFill().setSolidFill(rectColor);
      fullRect.getBorder().setTransparent();
      fullRect.sendToBack();
    }

    if (idx === 0) {
      const titleBox = slide.insertTextBox(
        slideObj.Title,
        pageWidth * 0.05, pageHeight * 0.03,
        pageWidth * 0.90, pageHeight * 0.15
      );
      titleBox.getText().getTextStyle()
        .setFontFamily('Roboto').setFontSize(32).setBold(true).setForegroundColor(textColor);

      const contentBox = slide.insertTextBox(
        slideObj.Content,
        pageWidth * 0.05, pageHeight * 0.20,
        pageWidth * 0.90, pageHeight * 0.60
      );
      const contentRange = contentBox.getText();
      contentRange.getTextStyle()
        .setFontFamily('Roboto').setFontSize(18).setForegroundColor(textColor);
      _applyTagAndRemoveTags(contentRange, "<b>", "</b>", tr => tr.setBold(true).setForegroundColor(textColor));
      _applyTagAndRemoveTags(contentRange, "<u>", "</u>", tr => tr.setUnderline(true).setForegroundColor(textColor));
      _applyFontTagAndRemove(contentRange, textColor);
      _stripAnyRemainingAngleTags(contentRange);

      const byBox = slide.insertTextBox(
        `By: ${author}`,
        pageWidth * 0.05, pageHeight * 0.85,
        pageWidth * 0.90, pageHeight * 0.10
      );
      byBox.getText().getTextStyle()
        .setFontFamily('Roboto').setFontSize(16).setItalic(true).setForegroundColor(textColor);
    } else {
      const contentBox = slide.insertTextBox(
        slideObj.Content,
        pageWidth * 0.05, pageHeight * 0.05,
        pageWidth * 0.90, pageHeight * 0.90
      );
      const contentRange = contentBox.getText();
      contentRange.getTextStyle()
        .setFontFamily('Roboto').setFontSize(18).setForegroundColor(textColor);
      _applyTagAndRemoveTags(contentRange, "<b>", "</b>", tr => tr.setBold(true).setForegroundColor(textColor));
      _applyTagAndRemoveTags(contentRange, "<u>", "</u>", tr => tr.setUnderline(true).setForegroundColor(textColor));
      _applyFontTagAndRemove(contentRange, textColor);
      _stripAnyRemainingAngleTags(contentRange);
    }
  });

  return pres.getUrl();
}


/**
 * Finds JSON substring between first “{” and last “}” (ignoring Markdown fences).
 */
function extractJson(str) {
  let noFences = str.replace(/```(?:json)?/g, '');
  const first = noFences.indexOf('{');
  const last  = noFences.lastIndexOf('}');
  if (first !== -1 && last !== -1 && last > first) {
    return noFences.substring(first, last + 1);
  }
  throw new Error('No JSON object found in response: ' + str);
}

/**
 * Calls the Groq API and returns the AI’s message.content.
 */
function callGroqWithErrors(promptText) {
  const payload = {
    model: GROQ_MODEL,
    messages: [{ role: 'user', content: promptText }],
    temperature: 0.7,
    max_tokens: 700,
    top_p: 1
  };
  const options = {
    method: 'post',
    contentType: 'application/json',
    headers: { Authorization: 'Bearer ' + GROQ_API_KEY },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };
  const resp = UrlFetchApp.fetch(GROQ_API_URL, options);
  const code = resp.getResponseCode();
  const text = resp.getContentText();
  if (code === 429) throw new Error('Rate limit exceeded');
  if (code >= 400) throw new Error(`API Error ${code}: ${text}`);
  let json;
  try {
    json = JSON.parse(text);
  } catch (e) {
    throw new Error('Invalid JSON from API: ' + text);
  }
  if (!json.choices || !json.choices[0] || !json.choices[0].message) {
    throw new Error('Unexpected API response format');
  }
  return json.choices[0].message.content;
}

/**
 * INTERNAL: Find and apply <b>…</b>, <u>…</u> tags, remove them.
 */
function _applyTagAndRemoveTags(textRange, openTag, closeTag, formattingCallback) {
  let fullText = textRange.asString();
  let openIndex = fullText.indexOf(openTag);
  while (openIndex !== -1) {
    const closeIndex = fullText.indexOf(closeTag, openIndex + openTag.length);
    if (closeIndex === -1) break;
    textRange.deleteText(openIndex, openIndex + openTag.length - 1);
    const newCloseIndex = closeIndex - openTag.length;
    textRange.deleteText(newCloseIndex, newCloseIndex + closeTag.length - 1);
    const startFormatting = openIndex;
    const endFormatting = newCloseIndex - 1;
    const subRange = textRange.getRange(startFormatting, endFormatting);
    formattingCallback(subRange);
    fullText = textRange.asString();
    openIndex = fullText.indexOf(openTag);
  }
}

/**
 * INTERNAL: Find <font size="…" face="…">…</font> tags, apply and remove.
 */
function _applyFontTagAndRemove(textRange, textColor) {
  let fullText = textRange.asString();
  let fontOpenStart = fullText.indexOf('<font ');
  while (fontOpenStart !== -1) {
    const fontTagClose = fullText.indexOf('>', fontOpenStart);
    if (fontTagClose === -1) break;
    const tagInside = fullText.substring(fontOpenStart + 6, fontTagClose).trim();
    const sizeMatch = tagInside.match(/size="(\d+)"/i);
    const faceMatch = tagInside.match(/face="([^"]+)"/i);
    const fontSize = sizeMatch ? parseInt(sizeMatch[1], 10) : null;
    const fontFace = faceMatch ? faceMatch[1] : null;
    const closeTag = '</font>';
    const closeIndex = fullText.indexOf(closeTag, fontTagClose + 1);
    if (closeIndex === -1) break;
    textRange.deleteText(closeIndex, closeIndex + closeTag.length - 1);
    textRange.deleteText(fontOpenStart, fontTagClose);
    const shiftAmount = (fontTagClose - fontOpenStart + 1);
    const startApply = fontOpenStart;
    const endApply = closeIndex - shiftAmount;
    const subRange = textRange.getRange(startApply, endApply);
    if (fontSize) subRange.getTextStyle().setFontSize(fontSize);
    if (fontFace) subRange.getTextStyle().setFontFamily(fontFace);
    subRange.getTextStyle().setForegroundColor(textColor);
    fullText = textRange.asString();
    fontOpenStart = fullText.indexOf('<font ');
  }
}

/**
 * INTERNAL: Remove any leftover <…> tags.
 */
function _stripAnyRemainingAngleTags(textRange) {
  let fullText = textRange.asString();
  let openIdx = fullText.indexOf('<');
  while (openIdx !== -1) {
    const closeIdx = fullText.indexOf('>', openIdx + 1);
    if (closeIdx === -1) break;
    textRange.deleteText(openIdx, closeIdx);
    fullText = textRange.asString();
    openIdx = fullText.indexOf('<');
  }
}
