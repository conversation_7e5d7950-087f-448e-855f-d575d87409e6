<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    body { font-family: Roboto, sans-serif; padding: 16px; }
    .field { margin-bottom: 16px; }
    label { display: block; margin-bottom: 4px; font-size: 14px; }
    input[type="text"], select, textarea, button {
      width: 100%; padding: 8px; font-size: 14px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;
    }
    textarea { resize: vertical; height: 80px; }
    button { background: #1a73e8; color: #fff; border: none; cursor: pointer; margin-top: 8px; }
    button:hover { background: #1669c1; }
    #status { margin-top: 12px; font-size: 14px; }
    details { margin-bottom: 16px; }
    summary { font-weight: bold; cursor: pointer; }
  </style>
</head>
<body>
  <h3>AI Slide Creator</h3>

  <!-- Mode Selection -->
  <div class="field">
    <label>Mode</label>
    <select id="modeSelect" onchange="switchMode()">
      <option value="create">Create Slides</option>
      <option value="organize">Organize Slides</option>
    </select>
  </div>

  <!-- Create Slides Mode -->
  <div id="createMode">
    <div class="field">
      <label for="topicCreate">Topic</label>
      <input id="topicCreate" type="text" placeholder="Presentation topic" />
    </div>
    <div class="field">
      <label for="authorCreate">Author Name</label>
      <input id="authorCreate" type="text" placeholder="Your name" />
    </div>
    <div class="field">
      <label for="themeCreate">Theme</label>
      <select id="themeCreate">
        <option value="Light">Light</option>
        <option value="Dark">Dark</option>
        <option value="Blue">Blue</option>
        <option value="Green">Green</option>
      </select>
    </div>

    <details>
      <summary>Slide Settings</summary>
      <div class="field" style="margin-top:8px;">
        <label for="slideCountCreate">Number of Slides</label>
        <select id="slideCountCreate" onchange="renderSlidesCreate()">
          <option value="1">1</option>
          <option value="2">2</option>
          <option value="3">3</option>
          <option value="4">4</option>
          <option value="5">5</option>
        </select>
      </div>
      <div id="slidesInputsCreate"></div>
    </details>

    <button onclick="submitCreate()">Generate Slides</button>
  </div>

  <!-- Organize Slides Mode -->
  <div id="organizeMode" style="display:none;">
    <div class="field">
      <label for="topicOrganize">Topic</label>
      <input id="topicOrganize" type="text" placeholder="Presentation topic" />
    </div>
    <div class="field">
      <label for="authorOrganize">Author Name</label>
      <input id="authorOrganize" type="text" placeholder="Your name" />
    </div>
    <div class="field">
      <label for="themeOrganize">Theme</label>
      <select id="themeOrganize">
        <option value="Light">Light</option>
        <option value="Dark">Dark</option>
        <option value="Blue">Blue</option>
        <option value="Green">Green</option>
      </select>
    </div>
    <div class="field">
      <label for="contentOrganize">Paste Content Here</label>
      <textarea id="contentOrganize" placeholder="Copy/paste content to be organized into slides…"></textarea>
    </div>
    <button onclick="submitOrganize()">Organize Slides</button>
  </div>

  <div id="status"></div>

  <script>
    function switchMode() {
      const mode = document.getElementById('modeSelect').value;
      document.getElementById('createMode').style.display   = mode === 'create' ? '' : 'none';
      document.getElementById('organizeMode').style.display = mode === 'organize' ? '' : 'none';
      document.getElementById('status').innerText = '';
    }

    function renderSlidesCreate() {
      const count = +document.getElementById('slideCountCreate').value;
      const container = document.getElementById('slidesInputsCreate');
      container.innerHTML = '';

      for (let i = 1; i <= count; i++) {
        const div = document.createElement('div');
        div.className = 'field';

        const label = document.createElement('label');
        label.innerText = `Slide ${i} DESCRIPTION`;
        div.appendChild(label);

        const textarea = document.createElement('textarea');
        textarea.id = `slideDescCreate${i}`;
        textarea.placeholder = 'Enter text describing what this slide should say…';
        div.appendChild(textarea);

        container.appendChild(div);
      }
    }

    function submitCreate() {
      const topic  = document.getElementById('topicCreate').value.trim();
      const author = document.getElementById('authorCreate').value.trim();
      const theme  = document.getElementById('themeCreate').value;
      const count  = +document.getElementById('slideCountCreate').value;
      if (!topic || !author) { alert('Topic and author required'); return; }

      const slides = [];
      for (let i = 1; i <= count; i++) {
        slides.push(document.getElementById(`slideDescCreate${i}`).value.trim());
      }
      document.getElementById('status').innerText = 'Generating…';
      google.script.run
        .withSuccessHandler(url => {
          document.getElementById('status').innerHTML = `<a href="${url}" target="_blank">Open Presentation</a>`;
        })
        .withFailureHandler(err => {
          document.getElementById('status').innerText = `Error: ${err.message}`;
        })
        .generateSlides({
          topic,
          author,
          theme,
          slideCount: count,
          slides
        });
    }

    function submitOrganize() {
      const topic   = document.getElementById('topicOrganize').value.trim();
      const author  = document.getElementById('authorOrganize').value.trim();
      const theme   = document.getElementById('themeOrganize').value;
      const content = document.getElementById('contentOrganize').value.trim();
      if (!topic || !author || !content) { alert('Topic, author, and content required'); return; }

      document.getElementById('status').innerText = 'Organizing…';
      google.script.run
        .withSuccessHandler(url => {
          document.getElementById('status').innerHTML = `<a href="${url}" target="_blank">Open Presentation</a>`;
        })
        .withFailureHandler(err => {
          document.getElementById('status').innerText = `Error: ${err.message}`;
        })
        .organizeSlides({
          topic,
          author,
          theme,
          content
        });
    }

    // Initial render for Create Slides mode
    renderSlidesCreate();
  </script>
</body>
</html>
